{"TESTING/test_unit_auth.py::TestAuthManager::test_create_user_success": true, "TESTING/test_unit_auth.py::TestAuthManager::test_create_user_with_role": true, "TESTING/test_unit_auth.py::TestAuthManager::test_create_user_duplicate_email": true, "TESTING/test_unit_auth.py::TestAuthManager::test_authenticate_user_success": true, "TESTING/test_unit_auth.py::TestAuthManager::test_authenticate_user_wrong_email": true, "TESTING/test_unit_auth.py::TestAuthManager::test_authenticate_user_wrong_password": true, "TESTING/test_unit_auth.py::TestAuthManager::test_get_user_by_id_exists": true, "TESTING/test_unit_auth.py::TestAuthManager::test_get_user_by_id_not_exists": true, "TESTING/test_unit_services.py::TestUserManagementService::test_get_all_users_active_only": true, "TESTING/test_unit_services.py::TestUserManagementService::test_get_users_by_role_student": true, "TESTING/test_unit_services.py::TestUserManagementService::test_get_users_by_role_admin": true, "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_success": true, "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_role": true, "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_invalid_role": true, "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_not_found": true, "TESTING/test_unit_services.py::TestUserManagementService::test_delete_user_success": true, "TESTING/test_unit_services.py::TestUserManagementService::test_delete_user_not_found": true, "TESTING/test_unit_services.py::TestUserManagementService::test_get_user_profile": true, "TESTING/test_unit_services.py::TestAcademicService::test_get_departments": true, "TESTING/test_unit_services.py::TestAcademicService::test_create_department_success": true, "TESTING/test_unit_services.py::TestAcademicService::test_create_department_duplicate_code": true, "TESTING/test_unit_services.py::TestAcademicService::test_update_department_success": true, "TESTING/test_unit_services.py::TestAcademicService::test_delete_department_success": true, "TESTING/test_unit_services.py::TestAcademicService::test_get_courses": true, "TESTING/test_unit_services.py::TestAcademicService::test_get_courses_by_lecturer": true, "TESTING/test_unit_services.py::TestAcademicService::test_get_academic_overview": true, "TESTING/test_unit_services.py::TestAcademicService::test_enroll_student_success": true, "TESTING/test_unit_services.py::TestAcademicService::test_get_student_enrollments": true, "TESTING/test_unit_services.py::TestServiceIntegration::test_user_service_with_academic_service": true, "TESTING/test_unit_services.py::TestServiceIntegration::test_academic_service_user_dependencies": true, "TESTING/test_integration.py::TestAuthManagerIntegration::test_auth_manager_with_user_management_service": true, "TESTING/test_integration.py::TestAuthManagerIntegration::test_auth_manager_with_academic_service": true, "TESTING/test_integration.py::TestAuthManagerIntegration::test_token_verification_with_user_retrieval": true, "TESTING/test_integration.py::TestUserManagementServiceIntegration::test_user_service_with_academic_service_enrollment": true, "TESTING/test_integration.py::TestUserManagementServiceIntegration::test_lecturer_dashboard_integration": true, "TESTING/test_integration.py::TestUserManagementServiceIntegration::test_user_role_change_integration": true, "TESTING/test_integration.py::TestAcademicServiceIntegration::test_course_creation_with_user_validation": true, "TESTING/test_integration.py::TestAcademicServiceIntegration::test_enrollment_workflow_integration": true, "TESTING/test_integration.py::TestAcademicServiceIntegration::test_academic_overview_integration": true, "TESTING/test_integration.py::TestAPIEndpointIntegration::test_register_login_workflow": true, "TESTING/test_integration.py::TestAPIEndpointIntegration::test_authenticated_api_access": true, "TESTING/test_integration.py::TestAPIEndpointIntegration::test_ai_tutor_integration": true, "TESTING/test_integration.py::TestAPIEndpointIntegration::test_course_management_integration": true, "TESTING/test_integration.py::TestDatabaseIntegration::test_user_course_relationship_integration": true, "TESTING/test_integration.py::TestDatabaseIntegration::test_cascade_operations_integration": true, "TESTING/test_system.py::TestStudentRegistrationWorkflow::test_student_registration_complete_workflow": true, "TESTING/test_system.py::TestStudentRegistrationWorkflow::test_student_course_enrollment_workflow": true, "TESTING/test_system.py::TestStudentRegistrationWorkflow::test_student_ai_tutor_workflow": true, "TESTING/test_system.py::TestAssignmentSubmissionWorkflow::test_assignment_creation_and_submission_workflow": true, "TESTING/test_system.py::TestAssignmentSubmissionWorkflow::test_assignment_grading_workflow": true, "TESTING/test_system.py::TestLecturerCourseManagementWorkflow::test_lecturer_course_creation_workflow": true, "TESTING/test_system.py::TestLecturerCourseManagementWorkflow::test_lecturer_student_management_workflow": true, "TESTING/test_system.py::TestAdminSystemManagementWorkflow::test_admin_department_management_workflow": true, "TESTING/test_system.py::TestAdminSystemManagementWorkflow::test_admin_user_management_workflow": true, "TESTING/test_security.py::TestRoleBasedAccessControl::test_student_access_restrictions": true, "TESTING/test_security.py::TestRoleBasedAccessControl::test_lecturer_access_restrictions": true, "TESTING/test_security.py::TestRoleBasedAccessControl::test_admin_full_access": true, "TESTING/test_security.py::TestRoleBasedAccessControl::test_cross_role_data_access_security": true, "TESTING/test_security.py::TestRoleBasedAccessControl::test_course_access_control": true, "TESTING/test_security.py::TestAuthenticationSecurity::test_password_hashing_security": true, "TESTING/test_security.py::TestAuthenticationSecurity::test_duplicate_email_prevention": true, "TESTING/test_security.py::TestAuthenticationSecurity::test_authentication_failure_handling": true, "TESTING/test_security.py::TestAuthenticationSecurity::test_token_authentication_security": true, "TESTING/test_security.py::TestAuthenticationSecurity::test_session_security": true, "TESTING/test_security.py::TestInputValidationSecurity::test_sql_injection_prevention": true, "TESTING/test_security.py::TestInputValidationSecurity::test_xss_prevention_in_inputs": true, "TESTING/test_security.py::TestInputValidationSecurity::test_input_length_validation": true, "TESTING/test_security.py::TestInputValidationSecurity::test_email_format_validation": true, "TESTING/test_security.py::TestAuthorizationSecurity::test_privilege_escalation_prevention": true}