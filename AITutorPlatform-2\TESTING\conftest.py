"""
Test configuration and fixtures for the AI-powered LMS
"""
import pytest
import os
import sys
import tempfile
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set test environment
os.environ["ENVIRONMENT"] = "testing"
os.environ["JWT_SECRET_KEY"] = "test_secret_key"
os.environ["GEMINI_API_KEY"] = "test_gemini_key"
os.environ["OPENAI_API_KEY"] = "test_openai_key"
os.environ["STRIPE_API_KEY"] = "test_stripe_key"
os.environ["STRIPE_WEBHOOK_SECRET"] = "test_webhook_secret"

from models import Base, User, UserRole, Department, Course, Semester
from database import get_db
from main import app
from auth import AuthManager

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session")
def test_db():
    """Create test database"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def db_session(test_db):
    """Create database session for testing"""
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture
def client(db_session):
    """Create test client"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture
def auth_manager():
    """Create AuthManager instance for testing"""
    return AuthManager()

@pytest.fixture
def test_user_data():
    """Sample user data for testing"""
    return {
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "testpassword123"
    }

@pytest.fixture
def test_admin_data():
    """Sample admin user data for testing"""
    return {
        "name": "Admin User",
        "email": "<EMAIL>",
        "password": "adminpassword123",
        "role": UserRole.ADMIN
    }

@pytest.fixture
def test_lecturer_data():
    """Sample lecturer user data for testing"""
    return {
        "name": "Lecturer User",
        "email": "<EMAIL>",
        "password": "lecturerpassword123",
        "role": UserRole.LECTURER
    }

@pytest.fixture
def sample_user(db_session, auth_manager, test_user_data):
    """Create a sample user in the database"""
    user = auth_manager.create_user(
        db=db_session,
        name=test_user_data["name"],
        email=test_user_data["email"],
        password=test_user_data["password"]
    )
    return user

@pytest.fixture
def sample_admin(db_session, auth_manager, test_admin_data):
    """Create a sample admin user in the database"""
    user = auth_manager.create_user(
        db=db_session,
        name=test_admin_data["name"],
        email=test_admin_data["email"],
        password=test_admin_data["password"],
        role=test_admin_data["role"]
    )
    return user

@pytest.fixture
def sample_lecturer(db_session, auth_manager, test_lecturer_data):
    """Create a sample lecturer user in the database"""
    user = auth_manager.create_user(
        db=db_session,
        name=test_lecturer_data["name"],
        email=test_lecturer_data["email"],
        password=test_lecturer_data["password"],
        role=test_lecturer_data["role"]
    )
    return user

@pytest.fixture
def sample_department(db_session):
    """Create a sample department"""
    department = Department(
        name="Computer Science",
        code="CS",
        description="Department of Computer Science"
    )
    db_session.add(department)
    db_session.commit()
    db_session.refresh(department)
    return department

@pytest.fixture
def sample_semester(db_session):
    """Create a sample semester"""
    from models import SemesterType
    from datetime import datetime

    semester = Semester(
        name="Fall 2024",
        semester_type=SemesterType.FALL,
        year=2024,
        start_date=datetime(2024, 9, 1),
        end_date=datetime(2024, 12, 15),
        registration_start=datetime(2024, 8, 1),
        registration_end=datetime(2024, 8, 31),
        is_current=True
    )
    db_session.add(semester)
    db_session.commit()
    db_session.refresh(semester)
    return semester

@pytest.fixture
def sample_program(db_session, sample_department):
    """Create a sample program"""
    from models import Program, ProgramType

    program = Program(
        name="Computer Science Bachelor",
        code="CS-BS",
        program_type=ProgramType.BACHELOR,
        department_id=sample_department.id,
        duration_years=4,
        total_credits=120,
        description="Bachelor of Science in Computer Science"
    )
    db_session.add(program)
    db_session.commit()
    db_session.refresh(program)
    return program

@pytest.fixture
def sample_course(db_session, sample_department, sample_lecturer, sample_semester):
    """Create a sample course"""
    course = Course(
        name="Introduction to Programming",
        code="CS101",
        description="Basic programming concepts",
        credits=3,
        department_id=sample_department.id,
        lecturer_id=sample_lecturer.id,
        semester_id=sample_semester.id,
        max_capacity=30
    )
    db_session.add(course)
    db_session.commit()
    db_session.refresh(course)
    return course

@pytest.fixture
def mock_gemini_service():
    """Mock Gemini AI service"""
    with patch('services.gemini_service.GeminiService') as mock:
        mock_instance = Mock()
        mock_instance.get_response.return_value = {
            "text": "This is a test AI response",
            "hasChart": False,
            "hasCode": False
        }
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def mock_whisper_service():
    """Mock Whisper service"""
    with patch('services.whisper_service.WhisperService') as mock:
        mock_instance = Mock()
        mock_instance.transcribe_audio.return_value = "This is transcribed text"
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def authenticated_headers(client, sample_user, auth_manager):
    """Get authentication headers for API requests"""
    token = auth_manager.create_access_token(sample_user.id)
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def admin_headers(client, sample_admin, auth_manager):
    """Get admin authentication headers for API requests"""
    token = auth_manager.create_access_token(sample_admin.id)
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def lecturer_headers(client, sample_lecturer, auth_manager):
    """Get lecturer authentication headers for API requests"""
    token = auth_manager.create_access_token(sample_lecturer.id)
    return {"Authorization": f"Bearer {token}"}
