["TESTING/test_integration.py::TestAPIEndpointIntegration::test_ai_tutor_integration", "TESTING/test_integration.py::TestAPIEndpointIntegration::test_authenticated_api_access", "TESTING/test_integration.py::TestAPIEndpointIntegration::test_course_management_integration", "TESTING/test_integration.py::TestAPIEndpointIntegration::test_register_login_workflow", "TESTING/test_integration.py::TestAcademicServiceIntegration::test_academic_overview_integration", "TESTING/test_integration.py::TestAcademicServiceIntegration::test_course_creation_with_user_validation", "TESTING/test_integration.py::TestAcademicServiceIntegration::test_enrollment_workflow_integration", "TESTING/test_integration.py::TestAuthManagerIntegration::test_auth_manager_with_academic_service", "TESTING/test_integration.py::TestAuthManagerIntegration::test_auth_manager_with_user_management_service", "TESTING/test_integration.py::TestAuthManagerIntegration::test_token_verification_with_user_retrieval", "TESTING/test_integration.py::TestDatabaseIntegration::test_cascade_operations_integration", "TESTING/test_integration.py::TestDatabaseIntegration::test_user_course_relationship_integration", "TESTING/test_integration.py::TestUserManagementServiceIntegration::test_lecturer_dashboard_integration", "TESTING/test_integration.py::TestUserManagementServiceIntegration::test_user_role_change_integration", "TESTING/test_integration.py::TestUserManagementServiceIntegration::test_user_service_with_academic_service_enrollment", "TESTING/test_security.py::TestAuthenticationSecurity::test_authentication_failure_handling", "TESTING/test_security.py::TestAuthenticationSecurity::test_duplicate_email_prevention", "TESTING/test_security.py::TestAuthenticationSecurity::test_password_hashing_security", "TESTING/test_security.py::TestAuthenticationSecurity::test_session_security", "TESTING/test_security.py::TestAuthenticationSecurity::test_token_authentication_security", "TESTING/test_security.py::TestAuthorizationSecurity::test_privilege_escalation_prevention", "TESTING/test_security.py::TestAuthorizationSecurity::test_unauthorized_access_prevention", "TESTING/test_security.py::TestInputValidationSecurity::test_email_format_validation", "TESTING/test_security.py::TestInputValidationSecurity::test_input_length_validation", "TESTING/test_security.py::TestInputValidationSecurity::test_sql_injection_prevention", "TESTING/test_security.py::TestInputValidationSecurity::test_xss_prevention_in_inputs", "TESTING/test_security.py::TestJWTSecurityTests::test_jwt_algorithm_security", "TESTING/test_security.py::TestJWTSecurityTests::test_jwt_secret_key_security", "TESTING/test_security.py::TestJWTSecurityTests::test_jwt_token_expiration_security", "TESTING/test_security.py::TestJWTSecurityTests::test_jwt_token_structure_security", "TESTING/test_security.py::TestJWTSecurityTests::test_jwt_token_tampering_security", "TESTING/test_security.py::TestRoleBasedAccessControl::test_admin_full_access", "TESTING/test_security.py::TestRoleBasedAccessControl::test_course_access_control", "TESTING/test_security.py::TestRoleBasedAccessControl::test_cross_role_data_access_security", "TESTING/test_security.py::TestRoleBasedAccessControl::test_lecturer_access_restrictions", "TESTING/test_security.py::TestRoleBasedAccessControl::test_student_access_restrictions", "TESTING/test_system.py::TestAdminSystemManagementWorkflow::test_admin_department_management_workflow", "TESTING/test_system.py::TestAdminSystemManagementWorkflow::test_admin_user_management_workflow", "TESTING/test_system.py::TestAssignmentSubmissionWorkflow::test_assignment_creation_and_submission_workflow", "TESTING/test_system.py::TestAssignmentSubmissionWorkflow::test_assignment_grading_workflow", "TESTING/test_system.py::TestLecturerCourseManagementWorkflow::test_lecturer_course_creation_workflow", "TESTING/test_system.py::TestLecturerCourseManagementWorkflow::test_lecturer_student_management_workflow", "TESTING/test_system.py::TestStudentRegistrationWorkflow::test_student_ai_tutor_workflow", "TESTING/test_system.py::TestStudentRegistrationWorkflow::test_student_course_enrollment_workflow", "TESTING/test_system.py::TestStudentRegistrationWorkflow::test_student_registration_complete_workflow", "TESTING/test_unit_auth.py::TestAuthManager::test_authenticate_user_success", "TESTING/test_unit_auth.py::TestAuthManager::test_authenticate_user_wrong_email", "TESTING/test_unit_auth.py::TestAuthManager::test_authenticate_user_wrong_password", "TESTING/test_unit_auth.py::TestAuthManager::test_create_access_token", "TESTING/test_unit_auth.py::TestAuthManager::test_create_refresh_token", "TESTING/test_unit_auth.py::TestAuthManager::test_create_user_duplicate_email", "TESTING/test_unit_auth.py::TestAuthManager::test_create_user_success", "TESTING/test_unit_auth.py::TestAuthManager::test_create_user_with_role", "TESTING/test_unit_auth.py::TestAuthManager::test_get_user_by_id_exists", "TESTING/test_unit_auth.py::TestAuthManager::test_get_user_by_id_not_exists", "TESTING/test_unit_auth.py::TestAuthManager::test_hash_password", "TESTING/test_unit_auth.py::TestAuthManager::test_verify_password_correct", "TESTING/test_unit_auth.py::TestAuthManager::test_verify_password_incorrect", "TESTING/test_unit_auth.py::TestAuthManager::test_verify_token_expired", "TESTING/test_unit_auth.py::TestAuthManager::test_verify_token_invalid", "TESTING/test_unit_auth.py::TestAuthManager::test_verify_token_valid", "TESTING/test_unit_auth.py::TestJWTTokens::test_token_contains_correct_claims", "TESTING/test_unit_auth.py::TestJWTTokens::test_token_expiration_times", "TESTING/test_unit_auth.py::TestPasswordContext::test_hash_password_different_each_time", "TESTING/test_unit_auth.py::TestPasswordContext::test_verify_password_case_sensitive", "TESTING/test_unit_services.py::TestAcademicService::test_create_department_duplicate_code", "TESTING/test_unit_services.py::TestAcademicService::test_create_department_success", "TESTING/test_unit_services.py::TestAcademicService::test_delete_department_success", "TESTING/test_unit_services.py::TestAcademicService::test_enroll_student_success", "TESTING/test_unit_services.py::TestAcademicService::test_get_academic_overview", "TESTING/test_unit_services.py::TestAcademicService::test_get_courses", "TESTING/test_unit_services.py::TestAcademicService::test_get_courses_by_lecturer", "TESTING/test_unit_services.py::TestAcademicService::test_get_departments", "TESTING/test_unit_services.py::TestAcademicService::test_get_student_enrollments", "TESTING/test_unit_services.py::TestAcademicService::test_update_department_success", "TESTING/test_unit_services.py::TestServiceIntegration::test_academic_service_user_dependencies", "TESTING/test_unit_services.py::TestServiceIntegration::test_user_service_with_academic_service", "TESTING/test_unit_services.py::TestUserManagementService::test_delete_user_not_found", "TESTING/test_unit_services.py::TestUserManagementService::test_delete_user_success", "TESTING/test_unit_services.py::TestUserManagementService::test_get_all_users_active_only", "TESTING/test_unit_services.py::TestUserManagementService::test_get_user_profile", "TESTING/test_unit_services.py::TestUserManagementService::test_get_users_by_role_admin", "TESTING/test_unit_services.py::TestUserManagementService::test_get_users_by_role_student", "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_invalid_role", "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_not_found", "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_role", "TESTING/test_unit_services.py::TestUserManagementService::test_update_user_success"]